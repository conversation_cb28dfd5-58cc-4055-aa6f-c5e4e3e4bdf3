package com.ctrip.dcs.tms.transport.application.query.impl;

import com.ctrip.dcs.tms.transport.application.dto.ComplianceCheckResultDTO;
import com.ctrip.dcs.tms.transport.application.dto.InternatSettleOCRDTO;
import com.ctrip.dcs.tms.transport.application.dto.OcrReqDTO;
import com.ctrip.dcs.tms.transport.application.dto.OcrResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.ComplianceStrategyEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.constant.VehicleAuditStatusEnum;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.NewOCRDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.InternationalEntryConfig;
import com.ctrip.dcs.tms.transport.infrastructure.common.qconfig.NewOcrFiledConfig;
import com.ctrip.dcs.tms.transport.infrastructure.port.repository.EnumRepository;
import com.ctrip.igt.framework.common.result.Result;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;

/**
 * InternationalEntryServiceImpl 测试类
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RunWith(MockitoJUnitRunner.class)
public class InternationalEntryServiceImplTest {

    @InjectMocks
    private InternationalEntryServiceImpl internationalEntryService;

    @Mock
    private InternationalEntryConfig internationalEntryConfig;

    @Mock
    private NewOcrFiledConfig newOcrFiledConfig;

    @Mock
    private EnumRepository enumRepository;

    @Before
    public void setUp() {
        // 设置韩国批准车辆列表
        List<String> koreaApproveList = Arrays.asList("1", "2", "3");
        when(internationalEntryConfig.getKoreaApproveVehicleList()).thenReturn(koreaApproveList);
        
        // 设置日本批准车辆列表
        List<String> japanApproveList = Arrays.asList("あ", "い", "う");
        when(internationalEntryConfig.getJapanApproveVehicleList()).thenReturn(japanApproveList);
    }

    @Test
    public void testAutoComplianceCheck_KoreaStrategy_Compliant() {
        // 测试韩国策略 - 合规情况
        String vehicleLicense = "123가4567";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                ComplianceStrategyEnum.THIRD_STRATEGY.getCode(), vehicleLicense, null);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.COMPLIANCE.getCode(), result.getAuditStatus());
        assertTrue(result.isCompliant());
        assertNull(result.getReason());
    }

    @Test
    public void testAutoComplianceCheck_KoreaStrategy_NonCompliant() {
        // 测试韩国策略 - 不合规情况
        String vehicleLicense = "ABC가4567";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                ComplianceStrategyEnum.THIRD_STRATEGY.getCode(), vehicleLicense, null);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.UNDISPOSED.getCode(), result.getAuditStatus());
        assertFalse(result.isCompliant());
        assertEquals("韩国车牌号码不包含批准的字符", result.getReason());
    }

    @Test
    public void testAutoComplianceCheck_SingaporeStrategy_EmptyData() {
        // 测试新加坡策略 - 空数据情况
        String vehicleLicense = "SH1234A";
        String ocrFieldValue = "{}";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                ComplianceStrategyEnum.SECOND_STRATEGY.getCode(), vehicleLicense, ocrFieldValue);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.DISQUALIFICATION.getCode(), result.getAuditStatus());
        assertFalse(result.isCompliant());
        assertEquals("合规资质证书信息为空", result.getReason());
    }

    @Test
    public void testAutoComplianceCheck_SingaporeStrategy_SHPrefix() {
        // 测试新加坡策略 - SH开头车牌
        String vehicleLicense = "SH1234A";
        String ocrFieldValue = "{\"complianceQualificationCertificates\":{\"VehicleNature\":\"private hire\",\"LicensePlateNo\":\"SH1234A\",\"VehicleScheme\":\"public service vehicle\"}}";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                ComplianceStrategyEnum.SECOND_STRATEGY.getCode(), vehicleLicense, ocrFieldValue);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.COMPLIANCE.getCode(), result.getAuditStatus());
        assertTrue(result.isCompliant());
        assertNull(result.getReason());
    }

    @Test
    public void testAutoComplianceCheck_JapanStrategy_EmptyOcrValue() {
        // 测试日本策略 - 空OCR值
        String vehicleLicense = "あ123";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                ComplianceStrategyEnum.FIRST_STRATEGY.getCode(), vehicleLicense, null);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.UNDISPOSED.getCode(), result.getAuditStatus());
        assertFalse(result.isCompliant());
        assertEquals("OCR字段值为空，需要人工审核", result.getReason());
    }

    @Test
    public void testAutoComplianceCheck_JapanStrategy_Compliant() {
        // 测试日本策略 - 合规情况
        String vehicleLicense = "あ123";
        String ocrFieldValue = "{\"vehicleCertiImg\":{\"useType\":\"事业用\"}}";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                ComplianceStrategyEnum.FIRST_STRATEGY.getCode(), vehicleLicense, ocrFieldValue);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.COMPLIANCE.getCode(), result.getAuditStatus());
        assertTrue(result.isCompliant());
        assertNull(result.getReason());
    }

    @Test
    public void testAutoComplianceCheck_JapanStrategy_NonCompliant() {
        // 测试日本策略 - 不合规情况
        String vehicleLicense = "X123";
        String ocrFieldValue = "{\"vehicleCertiImg\":{\"useType\":\"个人用\"}}";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                ComplianceStrategyEnum.FIRST_STRATEGY.getCode(), vehicleLicense, ocrFieldValue);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.DISQUALIFICATION.getCode(), result.getAuditStatus());
        assertFalse(result.isCompliant());
        assertEquals("车辆用途不是事业用且车牌号码不包含批准的字符", result.getReason());
    }

    @Test
    public void testAutoComplianceCheck_UnknownStrategy() {
        // 测试未知策略
        String vehicleLicense = "TEST123";
        ComplianceCheckResultDTO result = internationalEntryService.autoComplianceCheck(
                999, vehicleLicense, null);

        assertNotNull(result);
        assertEquals(VehicleAuditStatusEnum.UNDISPOSED.getCode(), result.getAuditStatus());
        assertFalse(result.isCompliant());
        assertEquals("未知的合规规则: 999", result.getReason());
    }

    @Test
    public void testAutoComplianceCheckResultDTO_StaticMethods() {
        // 测试DTO的静态方法
        ComplianceCheckResultDTO compliant = ComplianceCheckResultDTO.compliant();
        assertNotNull(compliant);
        assertEquals(VehicleAuditStatusEnum.COMPLIANCE.getCode(), compliant.getAuditStatus());
        assertTrue(compliant.isCompliant());
        assertNull(compliant.getReason());

        ComplianceCheckResultDTO nonCompliant = ComplianceCheckResultDTO.nonCompliant("测试原因");
        assertNotNull(nonCompliant);
        assertEquals(VehicleAuditStatusEnum.DISQUALIFICATION.getCode(), nonCompliant.getAuditStatus());
        assertFalse(nonCompliant.isCompliant());
        assertEquals("测试原因", nonCompliant.getReason());

        ComplianceCheckResultDTO manualReview = ComplianceCheckResultDTO.manualReview("需要人工审核");
        assertNotNull(manualReview);
        assertEquals(VehicleAuditStatusEnum.UNDISPOSED.getCode(), manualReview.getAuditStatus());
        assertFalse(manualReview.isCompliant());
        assertEquals("需要人工审核", manualReview.getReason());
    }

    @Test
    public void testQueryRequiredFiledList_WithFallback() {
        // 测试查询必填字段列表（使用收口方法）
        Long cityId = 1L;
        Long supplierId = 100L;
        List<String> sceneList = Arrays.asList("scene1", "scene2");

        RequiredFieldDTO cityField = new RequiredFieldDTO();
        cityField.setFieldName("cityField");
        RequiredFieldDTO countryField = new RequiredFieldDTO();
        countryField.setFieldName("countryField");

        List<RequiredFieldDTO> expectedFields = Arrays.asList(cityField, countryField);

        when(newOcrFiledConfig.getFieldListByCityWithFallback(cityId, sceneList))
                .thenReturn(expectedFields);

        Result<List<RequiredFieldDTO>> result = internationalEntryService.queryRequiredFiledList(cityId, supplierId, sceneList);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(expectedFields, result.getData());
    }

    @Test
    public void testIsInComplianceRuleGary_WithFallback() {
        // 测试获取合规配置（使用收口方法）
        Long cityId = 1L;

        OcrComplianceDTO expectedCompliance = new OcrComplianceDTO();
        expectedCompliance.setLocationId(cityId);
        expectedCompliance.setComplianceType(1);

        when(newOcrFiledConfig.getOcrComplianceByCityWithFallback(cityId))
                .thenReturn(expectedCompliance);

        OcrComplianceDTO result = internationalEntryService.isInComplianceRuleGary(cityId);

        assertNotNull(result);
        assertEquals(expectedCompliance, result);
    }

    @Test
    public void testUseNewOCR_WithFallback() {
        // 测试使用新OCR配置（使用收口方法）
        Long cityId = 1L;
        Long supplierId = 100L;

        NewOCRDTO newOcrDto = new NewOCRDTO();
        newOcrDto.setLocationId(cityId);
        newOcrDto.setOcrFieldList(Arrays.asList("field1", "field2"));

        when(newOcrFiledConfig.getNewOcrByCityWithFallback(cityId))
                .thenReturn(newOcrDto);

        InternatSettleOCRDTO result = internationalEntryService.useNewOCR(cityId, supplierId);

        assertNotNull(result);
        assertTrue(result.getUseNewOCR());
        assertEquals(newOcrDto.getOcrFieldList(), result.getNewOCRFieldList());
    }

    @Test
    public void testUseNewOCR_NoConfig() {
        // 测试使用新OCR配置（无配置情况）
        Long cityId = 1L;
        Long supplierId = 100L;

        when(newOcrFiledConfig.getNewOcrByCityWithFallback(cityId))
                .thenReturn(null);

        InternatSettleOCRDTO result = internationalEntryService.useNewOCR(cityId, supplierId);

        assertNotNull(result);
        assertFalse(result.getUseNewOCR());
        assertTrue(result.getNewOCRFieldList().isEmpty());
    }

    @Test
    public void testOcrRecognition_InvalidParameters() {
        // 测试OCR识别 - 无效参数
        OcrReqDTO ocrReqDTO = new OcrReqDTO();
        // 不设置必要参数，应该返回失败

        Result<List<OcrResultDTO>> result = internationalEntryService.ocrRecognition(ocrReqDTO);

        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("INVALID_PARAMETERS", result.getCode());
        assertEquals("参数验证失败", result.getMsg());
    }
}
