package com.ctrip.dcs.tms.transport.application.query;

import java.util.List;

import com.ctrip.dcs.tms.transport.application.dto.ComplianceCheckResultDTO;
import com.ctrip.dcs.tms.transport.application.dto.InternatSettleOCRDTO;
import com.ctrip.dcs.tms.transport.application.dto.OcrReqDTO;
import com.ctrip.dcs.tms.transport.application.dto.OcrRecoResultDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.OcrComplianceDTO;
import com.ctrip.dcs.tms.transport.infrastructure.common.dto.RequiredFieldDTO;
import com.ctrip.igt.framework.common.result.Result;

public interface InternationalEntryService {

    /**
     * 是否在安全合规的范围内
     *
     * @param cityId     城市ID
     * @param supplierId 供应商id
     * @return {@link Result }<{@link Boolean }>
     */
    Result<Boolean> isInComplianceRuleGary(Long cityId, Long supplierId);


    /**
     * 查询所需字段列表
     *
     * @param cityId     城市ID
     * @param supplierId 供应商id
     * @param sceneList
     * @return {@link Result }<{@link List }<{@link RequiredFieldDTO }>>
     */
    Result<List<RequiredFieldDTO>> queryRequiredFiledList(Long cityId, Long supplierId, List<String> sceneList);


    /**
     * ocr识别
     *
     * @param ocrReqDTO ocr req 到
     * @return {@link Result }<{@link List }<{@link OcrResultDTO }>>
     */
    Result<List<OcrResultDTO>> ocrRecognition(OcrReqDTO ocrReqDTO);

    ComplianceCheckResultDTO autoComplianceCheck(Integer inComplianceRule, String vehicleLicense, String newOcrFieldValue);

    OcrComplianceDTO isInComplianceRuleGary(Long cityId);


    InternatSettleOCRDTO useNewOCR(Long cityId, Long supplierId);

}
